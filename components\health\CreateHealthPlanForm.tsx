import React, { useState } from 'react';
import { View, ScrollView, TouchableOpacity, Platform } from 'react-native';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Button } from '../ui/button';
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
  CardHeader,
} from '../ui/card';
import { Text } from '../ui/text';
import { HealthPlanType, healthPlansData } from '@/data/healthPlansData';
import { cn } from '@/lib/utils';
import {
  Coffee,
  Utensils,
  Moon,
  Cookie,
  Zap,
  Beef,
  Wheat,
  Droplet,
  Shield,
  Bone,
  Eye,
  Heart,
} from 'lucide-react-native';

interface CreateHealthPlanFormProps {
  onPlanCreated: () => void;
  onCancel: () => void;
}

// Helper function to get meal info (color, icon, label)
const getMealInfo = (mealType: 'breakfast' | 'lunch' | 'dinner' | 'snacks') => {
  const mealData = {
    breakfast: {
      color: 'text-orange-500',
      icon: Coffee,
      label: 'Breakfast',
      bgColor: 'bg-orange-50',
    },
    lunch: {
      color: 'text-green-500',
      icon: Utensils,
      label: 'Lunch',
      bgColor: 'bg-green-50',
    },
    dinner: {
      color: 'text-blue-500',
      icon: Moon,
      label: 'Dinner',
      bgColor: 'bg-blue-50',
    },
    snacks: {
      color: 'text-purple-500',
      icon: Cookie,
      label: 'Snacks',
      bgColor: 'bg-purple-50',
    },
  };
  return mealData[mealType];
};

export function CreateHealthPlanForm({
  onPlanCreated,
  onCancel,
}: CreateHealthPlanFormProps) {
  const createHealthPlanMutation = useMutation(
    api.healthPlans.createHealthPlan,
  );

  const [planType, setPlanType] = useState<HealthPlanType>('weight');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleCreatePlan = async () => {
    try {
      const selectedPlanData = healthPlansData.find(
        (plan) => plan.type === planType,
      );

      if (!selectedPlanData) {
        console.error('Selected plan type not found:', planType);
        // TODO: Show error message to user
        return;
      }

      await createHealthPlanMutation({
        isActive: true, // Automatically activate the new plan
        dailyCalories: selectedPlanData.dailyCalories,
        type: planType,
        startDate: startDate.toISOString(),
        endDate: endDate?.toISOString(),
      });
      onPlanCreated();
    } catch (error) {
      console.error('Failed to create health plan:', error);
      // TODO: Show error message to user
    }
  };

  const onStartDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || startDate;
    setShowStartDatePicker(false);
    setStartDate(currentDate);
  };

  const onEndDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || endDate;
    setShowEndDatePicker(false);
    setEndDate(currentDate);
  };

  const selectedPlan = healthPlansData.find((plan) => plan.type === planType);
  const totalCalories = selectedPlan
    ? selectedPlan.dailyCalories.breakfast +
      selectedPlan.dailyCalories.lunch +
      selectedPlan.dailyCalories.dinner +
      selectedPlan.dailyCalories.snacks
    : 0;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <ScrollView
      className="flex-1 bg-white px-4 mb-6"
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <View className="pt-6 pb-8 items-center">
        <Text className="font-poppins-bold text-3xl text-gray-900 mb-1">
          Create Health Plan
        </Text>
        <Text className="font-poppins-regular text-sm text-gray-600 text-center leading-5">
          Choose a plan that aligns with your health goals
        </Text>
      </View>

      {/* Plan Selection */}
      <View className="mb-6">
        <Text className="font-poppins-semibold text-lg text-gray-900 mb-3">
          Select Your Plan
        </Text>
        <View className="gap-3">
          {healthPlansData.map((plan) => (
            <TouchableOpacity
              key={plan.type}
              className={cn(
                'bg-white rounded-lg p-4 border-2 shadow-sm',
                planType === plan.type
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-200',
              )}
              onPress={() => setPlanType(plan.type)}
              activeOpacity={0.7}
            >
              <View className="flex-row justify-between items-center mb-2">
                <Text
                  className={cn(
                    'font-poppins-semibold text-lg flex-1',
                    planType === plan.type ? 'text-primary' : 'text-gray-900',
                  )}
                >
                  {plan.title}
                </Text>
                <View
                  className={cn(
                    'w-5 h-5 rounded-full border-2',
                    planType === plan.type
                      ? 'border-primary bg-primary'
                      : 'border-gray-300 bg-white',
                  )}
                />
              </View>
              <Text className="font-poppins-regular text-sm text-gray-600 leading-4 mb-3">
                {plan.description}
              </Text>
              <View className="bg-gray-50 rounded-lg p-4">
                <Text className="font-poppins-medium text-xs text-gray-700 mb-3 uppercase tracking-wide">
                  Daily Calories
                </Text>

                {/* Meal breakdown with icons and labels */}
                <View className="gap-2 mb-3">
                  {(['breakfast', 'lunch', 'dinner', 'snacks'] as const).map(
                    (mealType) => {
                      const mealInfo = getMealInfo(mealType);
                      const IconComponent = mealInfo.icon;
                      const calories = plan.dailyCalories[mealType];

                      return (
                        <View
                          key={mealType}
                          className={cn(
                            'flex-row items-center justify-between p-2 rounded-md',
                            mealInfo.bgColor,
                          )}
                        >
                          <View className="flex-row items-center gap-2">
                            <IconComponent
                              size={16}
                              className={mealInfo.color}
                            />
                            <Text
                              className={cn(
                                'font-poppins-medium text-sm',
                                mealInfo.color,
                              )}
                            >
                              {mealInfo.label}
                            </Text>
                          </View>
                          <Text
                            className={cn(
                              'font-poppins-semibold text-sm',
                              mealInfo.color,
                            )}
                          >
                            {calories} cal
                          </Text>
                        </View>
                      );
                    },
                  )}
                </View>

                {/* Total calories */}
                <View className="border-t border-gray-200 pt-2">
                  <Text className="font-poppins-semibold text-sm text-primary text-center">
                    Total:{' '}
                    {plan.dailyCalories.breakfast +
                      plan.dailyCalories.lunch +
                      plan.dailyCalories.dinner +
                      plan.dailyCalories.snacks}{' '}
                    cal
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Selected Plan Summary */}
      {selectedPlan && (
        <Card className="mb-4 bg-secondary rounded-lg shadow-md">
          <CardHeader>
            <CardTitle className="mb-1 font-poppins-semibold text-xl">
              Plan Summary
            </CardTitle>
            <CardDescription>
              <Text className="text-muted-foreground text-sm">
                {selectedPlan.title} • {totalCalories} calories daily
              </Text>
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Date Selection */}
      <View className="mb-6">
        <Text className="font-poppins-semibold text-lg text-gray-900 mb-3">
          Plan Duration
        </Text>

        <View className="flex-row gap-3">
          <TouchableOpacity
            className="flex-1 bg-gray-50 rounded-lg p-4 border border-gray-200 items-center"
            onPress={() => setShowStartDatePicker(true)}
            activeOpacity={0.7}
          >
            <Text className="font-poppins-medium text-sm text-gray-600 mb-1">
              Start Date
            </Text>
            <Text className="font-poppins-semibold text-sm text-gray-900 text-center">
              {formatDate(startDate)}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-1 bg-gray-50 rounded-lg p-4 border border-gray-200 items-center"
            onPress={() => setShowEndDatePicker(true)}
            activeOpacity={0.7}
          >
            <Text className="font-poppins-medium text-sm text-gray-600 mb-1">
              End Date
            </Text>
            <Text className="font-poppins-semibold text-sm text-gray-900 text-center">
              {endDate ? formatDate(endDate) : 'Optional'}
            </Text>
          </TouchableOpacity>
        </View>

        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={onStartDateChange}
            minimumDate={new Date()}
          />
        )}

        {showEndDatePicker && (
          <DateTimePicker
            value={endDate || new Date()}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={onEndDateChange}
            minimumDate={startDate}
          />
        )}
      </View>

      {/* Action Buttons */}
      <View className="flex-row gap-3 pb-6 mt-4">
        <Button
          variant="outline"
          onPress={onCancel}
          className="flex-1 bg-transparent border-2 border-gray-300 rounded-lg py-3"
        >
          <Text className="font-poppins-semibold text-sm text-gray-700">
            Cancel
          </Text>
        </Button>
        <Button
          onPress={handleCreatePlan}
          className="flex-1 bg-primary rounded-lg py-3 shadow-sm"
        >
          <Text className="font-poppins-semibold text-sm text-white">
            Create Plan
          </Text>
        </Button>
      </View>
    </ScrollView>
  );
}
