import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface HealthSummaryProps {
  activePlan: any; // Replace 'any' with actual type from Convex if available
  mealsSummary: any; // Replace 'any' with actual type from Convex if available
}

const HealthSummary: React.FC<HealthSummaryProps> = ({
  activePlan,
  mealsSummary,
}) => {
  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>Health Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <View className="mb-2">
          <Text className="text-lg font-semibold">Active Plan:</Text>
          <Text>{activePlan ? activePlan.name : 'No active plan'}</Text>
        </View>
        <View>
          <Text className="text-lg font-semibold">Meals Summary:</Text>
          {mealsSummary ? (
            <>
              <Text>Total Calories: {mealsSummary.totalCalories}</Text>
              <Text>Total Protein: {mealsSummary.totalProtein}g</Text>
              <Text>Total Carbs: {mealsSummary.totalCarbs}g</Text>
              <Text>Total Fat: {mealsSummary.totalFat}g</Text>
            </>
          ) : (
            <Text>No meal summary available for today.</Text>
          )}
        </View>
      </CardContent>
    </Card>
  );
};

export default HealthSummary;
