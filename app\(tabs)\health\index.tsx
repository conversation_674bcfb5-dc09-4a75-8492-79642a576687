import React from 'react';
import { StyleSheet } from 'react-native';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { HealthDashboard } from '@/components/health/HealthDashboard';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Header } from '@/components/ui/Header';

const Health = () => {
  //

  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const dateString = selectedDate.toISOString().split('T')[0];

  const activePlan = useQuery(api.healthPlans.getActiveHealthPlan);
  const mealsSummary = useQuery(api.meals.getMealsSummary, {
    startDate: dateString,
    endDate: dateString,
  });

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaView edges={['top']} className="flex-1 bg-background">
        <Header title="My Health" />
        <HealthDashboard
          meals={[]}
          activePlan={activePlan}
          mealsSummary={mealsSummary}
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
        />
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};

export default Health;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
