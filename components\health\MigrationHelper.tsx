import React, { useState } from 'react';
import { View } from 'react-native';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Button } from '../ui/button';
import { Text } from '../ui/text';
import { Card, CardContent } from '../ui/card';

export function MigrationHelper() {
  const [isLoading, setIsLoading] = useState(false);
  const [migrationResult, setMigrationResult] = useState<string | null>(null);
  
  const migrateHealthPlans = useMutation(api.migrations.migrateHealthPlansWithNutrition);

  const handleMigration = async () => {
    setIsLoading(true);
    setMigrationResult(null);
    
    try {
      const result = await migrateHealthPlans();
      setMigrationResult(result.message);
    } catch (error) {
      setMigrationResult(`Migration failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View className="mx-4 mt-4">
      <Card className="rounded-lg shadow-md bg-card">
        <CardContent className="p-4">
          <Text className="font-bold text-lg text-foreground mb-2">
            Health Plan Migration
          </Text>
          <Text className="text-sm text-muted-foreground mb-4">
            Update your existing health plans with detailed nutrition targets.
          </Text>
          
          <Button 
            onPress={handleMigration} 
            disabled={isLoading}
            className="mb-4"
          >
            <Text>{isLoading ? 'Migrating...' : 'Update Health Plans'}</Text>
          </Button>
          
          {migrationResult && (
            <View className="p-3 bg-secondary/50 rounded-lg">
              <Text className="text-sm text-foreground">{migrationResult}</Text>
            </View>
          )}
        </CardContent>
      </Card>
    </View>
  );
}
