import React, { useState } from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import LogMoodSheet from '@/components/mood/log-mood-sheet';
import { SmilePlus } from 'lucide-react-native';

const MentalHealthScreen: React.FC = () => {
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  return (
    <View className="flex-1 p-4">
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Mental Health</CardTitle>
        </CardHeader>
        <CardContent>
          <Text>
            Details about your mental well-being, stress levels, and mindfulness
            practices will go here.
          </Text>
          <Button onPress={() => setIsSheetOpen(true)} className="mt-4">
            <SmilePlus size={18} color="white" className="mr-2" />
            <Text>Log Mood</Text>
          </Button>
          {/* Add more mental health related components here */}
        </CardContent>
      </Card>
      <View style={{ flex: 1, position: 'absolute', bottom: 0, left: 0, right: 0 }}>
        <LogMoodSheet
          isOpen={isSheetOpen}
          onClose={() => setIsSheetOpen(false)}
        />
      </View>
    </View>
  );
};

export default MentalHealthScreen;
