import React, { useState } from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import { Plus, Droplet, Coffee, Wine, Beer, Milk } from 'lucide-react-native';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { ImagePickerButton } from '../camera/ImagePickerButton';
import { Text } from '../ui/text';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '../ui/dialog';
import { Progress } from '../ui/progress';
import { cn } from '@/lib/utils';

type MealType = 'Breakfast' | 'Lunch' | 'Dinner' | 'Snack';
type DrinkType =
  | 'Water'
  | 'Coffee'
  | 'Tea'
  | 'Juice'
  | 'Soda'
  | 'Alcohol'
  | 'Milk';

const drinkTypeToMealType: Record<
  DrinkType,
  'breakfast' | 'lunch' | 'dinner' | 'snacks'
> = {
  Water: 'snacks',
  Coffee: 'breakfast',
  Tea: 'breakfast',
  Juice: 'breakfast',
  Soda: 'snacks',
  Alcohol: 'snacks',
  Milk: 'snacks',
};

const defaultDrinkNutrition = {
  Water: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    description: 'Pure water',
  },
  Coffee: {
    calories: 5,
    protein: 0.5,
    carbs: 0.5,
    fat: 0.1,
    description: 'Black coffee',
  },
  Tea: {
    calories: 2,
    protein: 0.1,
    carbs: 0.5,
    fat: 0,
    description: 'Unsweetened tea',
  },
  Juice: {
    calories: 110,
    protein: 1,
    carbs: 27,
    fat: 0.5,
    description: 'Orange juice',
  },
  Soda: { calories: 150, protein: 0, carbs: 39, fat: 0, description: 'Cola' },
  Alcohol: {
    calories: 100,
    protein: 0,
    carbs: 10,
    fat: 0,
    description: 'Generic alcoholic drink',
  },
  Milk: {
    calories: 100,
    protein: 8,
    carbs: 12,
    fat: 2,
    description: 'Skim milk',
  },
};

export function CaloryTracker() {
  const [selectedMeal, setSelectedMeal] = useState<MealType>('Breakfast');
  const [foodImage, setFoodImage] = useState<string | null>(null);
  const [isMealSheetVisible, setMealSheetVisible] = useState(false);
  const [isDrinkSheetVisible, setDrinkSheetVisible] = useState(false);
  const [selectedDrink, setSelectedDrink] = useState<DrinkType | null>(null);
  const [drinkAmount, setDrinkAmount] = useState('250');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<any>(null);

  const createMeal = useMutation(api.meals.createMealWithImage);
  const createDrinkMutation = useMutation(api.drinks.createDrink);
  const activePlan = useQuery(api.healthPlans.getActiveHealthPlan);

  const mealTypes: MealType[] = ['Breakfast', 'Lunch', 'Dinner', 'Snack'];

  const handleImageSelected = async (uri: string) => {
    setFoodImage(uri);
    setIsAnalyzing(true);
    try {
      const result = await createMeal({
        imageUrl: uri,
        type: selectedMeal.toLowerCase() as any,
        date: new Date().toISOString().split('T')[0],
      });
      if (result) {
        setAnalysis(result.analysis);
      }
      setMealSheetVisible(true);
    } catch (error) {
      console.error('Error creating meal:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleAddDrink = async () => {
    if (selectedDrink && drinkAmount) {
      const amountMl = Number(drinkAmount);
      const nutrition = defaultDrinkNutrition[selectedDrink];
      const mealTypeForDrink = drinkTypeToMealType[selectedDrink];

      try {
        await createDrinkMutation({
          name: selectedDrink,
          description: nutrition.description,
          calories: (nutrition.calories / 250) * amountMl,
          protein: (nutrition.protein / 250) * amountMl,
          carbs: (nutrition.carbs / 250) * amountMl,
          fat: (nutrition.fat / 250) * amountMl,
          type: mealTypeForDrink,
          date: new Date().toISOString().split('T')[0],
        });
        console.log(`Added ${amountMl}ml of ${selectedDrink} to Convex`);
        setDrinkSheetVisible(false);
        setSelectedDrink(null);
        setDrinkAmount('250');
      } catch (error) {
        console.error('Error adding drink:', error);
      }
    }
  };

  return (
    <View className="p-4 bg-white rounded-lg mx-4 mt-4 shadow-md">
      <View className="flex flex-row justify-between items-center mb-4">
        <Text className="font-semibold text-lg text-gray-900">
          Log Your Meal
        </Text>
        <TouchableOpacity
          className="flex flex-row items-center bg-primary/10 px-3 py-2 rounded-md"
          onPress={() => setDrinkSheetVisible(true)}
        >
          <Plus size={20} color="hsl(var(--primary))" />
          <Text className="font-medium text-sm text-primary ml-1">
            Add Drink
          </Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        className="flex flex-col items-center p-6 rounded-lg bg-gray-50 border-2 border-gray-200 border-dashed w-full"
        onPress={() => setMealSheetVisible(true)}
      >
        <ImagePickerButton onImageSelected={handleImageSelected} />
      </TouchableOpacity>

      <Dialog open={isMealSheetVisible} onOpenChange={setMealSheetVisible}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Log Your Meal</DialogTitle>
            <DialogDescription>
              Select meal type and view AI analysis.
            </DialogDescription>
          </DialogHeader>

          <View className="flex flex-row mb-4">
            {mealTypes.map((meal) => (
              <TouchableOpacity
                key={meal}
                className={cn(
                  'flex-1 py-2 px-1 rounded-md bg-gray-100 mx-1 items-center',
                  selectedMeal === meal && 'bg-primary',
                )}
                onPress={() => setSelectedMeal(meal)}
              >
                <Text
                  className={cn(
                    'font-medium text-sm text-gray-600',
                    selectedMeal === meal && 'text-white',
                  )}
                >
                  {meal}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {foodImage && (
            <View className="rounded-lg overflow-hidden mb-4">
              <Image
                source={{ uri: foodImage }}
                className="w-full h-52 object-cover rounded-lg"
              />
              {isAnalyzing ? (
                <View className="p-4 items-center">
                  <Text className="font-medium text-base text-gray-600">
                    Analyzing your meal...
                  </Text>
                </View>
              ) : (
                analysis && (
                  <View className="p-4 bg-white">
                    <Text className="font-medium text-sm text-gray-600 mb-1">
                      AI Analysis
                    </Text>
                    <Text className="font-bold text-xl text-primary mb-1">
                      {analysis.calories} calories
                    </Text>
                    <Text className="font-normal text-base text-gray-700 mb-4">
                      {analysis.description}
                    </Text>

                    <View className="flex flex-row justify-around mb-4 py-2 border-t border-b border-gray-200">
                      <View className="items-center">
                        <Text className="font-bold text-lg text-gray-900">
                          {analysis.protein}g
                        </Text>
                        <Text className="font-medium text-sm text-gray-600 mt-1">
                          Protein
                        </Text>
                      </View>
                      <View className="items-center">
                        <Text className="font-bold text-lg text-gray-900">
                          {analysis.carbs}g
                        </Text>
                        <Text className="font-medium text-sm text-gray-600 mt-1">
                          Carbs
                        </Text>
                      </View>
                      <View className="items-center">
                        <Text className="font-bold text-lg text-gray-900">
                          {analysis.fat}g
                        </Text>
                        <Text className="font-medium text-sm text-gray-600 mt-1">
                          Fat
                        </Text>
                      </View>
                    </View>

                    {activePlan && (
                      <View className="mt-4">
                        <Text className="font-semibold text-sm text-gray-800 mb-2">
                          Health Plan Target
                        </Text>
                        <Progress
                          value={Math.min(
                            100,
                            (analysis.calories /
                              activePlan.dailyCalories[
                                selectedMeal.toLowerCase() as keyof typeof activePlan.dailyCalories
                              ]) *
                              100,
                          )}
                          indicatorClassName={
                            analysis.calories >
                            activePlan.dailyCalories[
                              selectedMeal.toLowerCase() as keyof typeof activePlan.dailyCalories
                            ]
                              ? 'bg-destructive'
                              : 'bg-primary'
                          }
                        />
                        <Text className="font-medium text-sm text-gray-600 text-right mt-1">
                          {analysis.calories} /{' '}
                          {
                            activePlan.dailyCalories[
                              selectedMeal.toLowerCase() as keyof typeof activePlan.dailyCalories
                            ]
                          }{' '}
                          cal
                        </Text>
                      </View>
                    )}
                  </View>
                )
              )}
            </View>
          )}

          <DialogFooter>
            <DialogClose asChild>
              <Button onPress={() => setMealSheetVisible(false)}>
                <Text>Log Meal</Text>
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isDrinkSheetVisible} onOpenChange={setDrinkSheetVisible}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add Drink</DialogTitle>
            <DialogDescription>Select a drink and amount.</DialogDescription>
          </DialogHeader>

          <View className="flex flex-row flex-wrap justify-between mb-4">
            {Object.keys(defaultDrinkNutrition).map((type) => {
              const drinkType = type as DrinkType;
              const icon = getDrinkIcon(drinkType);
              return (
                <TouchableOpacity
                  key={drinkType}
                  className={cn(
                    'w-[30%] flex flex-col items-center p-4 rounded-md mb-4 bg-gray-50',
                    selectedDrink === drinkType && 'bg-primary/20',
                  )}
                  onPress={() => setSelectedDrink(drinkType)}
                >
                  {icon}
                  <Text
                    className={cn(
                      'font-medium text-sm text-gray-700 mt-2',
                      selectedDrink === drinkType && 'text-primary',
                    )}
                  >
                    {drinkType}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>

          <View className="mb-4">
            <Text className="font-medium text-base text-gray-800 mb-2">
              Amount (ml)
            </Text>
            <Input
              className="border border-gray-300 rounded-md p-3 text-lg font-medium text-center"
              keyboardType="number-pad"
              value={drinkAmount}
              onChangeText={setDrinkAmount}
              maxLength={4}
            />
          </View>

          <DialogFooter>
            <DialogClose asChild>
              <Button
                onPress={handleAddDrink}
                disabled={!selectedDrink || !drinkAmount}
              >
                <Text>Add Drink</Text>
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </View>
  );
}

const getDrinkIcon = (type: DrinkType) => {
  switch (type) {
    case 'Water':
      return <Droplet size={24} color="hsl(var(--primary))" />;
    case 'Coffee':
      return <Coffee size={24} color="#795548" />;
    case 'Tea':
      return <Coffee size={24} color="#4CAF50" />;
    case 'Juice':
      return <Wine size={24} color="#FF9800" />;
    case 'Soda':
      return <Beer size={24} color="#F44336" />;
    case 'Alcohol':
      return <Wine size={24} color="#9C27B0" />;
    case 'Milk':
      return <Milk size={24} color="#90A4AE" />;
    default:
      return null;
  }
};
