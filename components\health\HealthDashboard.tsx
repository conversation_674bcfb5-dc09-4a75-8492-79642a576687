import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { theme } from '@/constants/theme';
import { HealthPlanCard } from './HealthPlanCard';
import { CaloryTracker } from './CaloryTracker';
import { DailyOverview } from './DailyOverview';
import { HealthSuggestions } from './HealthSuggestions';
import { DateSelector } from './DateSelector';

import { Doc } from '@/convex/_generated/dataModel';

type Meal = Doc<'meal'>;
type HealthPlan = Doc<'healthPlan'>;

interface HealthDashboardProps {
  activePlan: HealthPlan | undefined | null;
  meals: Meal[] | undefined;
  mealsSummary:
    | {
        totalCalories: number;
        totalProtein: number;
        totalCarbs: number;
        totalFat: number;
        mealsByType: {
          breakfast: number;
          lunch: number;
          dinner: number;
          snacks: number;
        };
      }
    | undefined;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

export function HealthDashboard({
  activePlan,
  meals,
  mealsSummary,
  selectedDate,
  onDateChange,
}: HealthDashboardProps) {
  return (
    <ScrollView
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.content}
    >
      <DateSelector selectedDate={selectedDate} onDateChange={onDateChange} />
      <HealthPlanCard activePlan={activePlan || undefined} />
      <DailyOverview mealsSummary={mealsSummary} />
      <CaloryTracker />
      <HealthSuggestions />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  content: {
    paddingBottom: theme.spacing.xl,
  },
});
