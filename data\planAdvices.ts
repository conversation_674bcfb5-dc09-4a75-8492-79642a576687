export interface PlanAdvice {
  text: string;
  icon: string; // Emoji or icon name
  color: string; // Hex or Tailwind color
}

export interface PlanAdvicesAndActions {
  advices: PlanAdvice[];
  recommendedActions: PlanAdvice[];
}

export const planAdvices: Record<string, PlanAdvicesAndActions> = {
  weight: {
    advices: [
      { text: 'Track your meals daily', icon: '📝', color: '#34d399' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      {
        text: 'Aim for gradual, sustainable weight loss',
        icon: '📉',
        color: '#fbbf24',
      },
      { text: 'Avoid skipping meals', icon: '⏰', color: '#f87171' },
      { text: 'Limit sugary snacks', icon: '🍬', color: '#f472b6' },
    ],
    recommendedActions: [
      {
        text: 'Exercise at least 3 times a week',
        icon: '🏃‍♂️',
        color: '#f59e42',
      },
      { text: 'Prioritize whole foods', icon: '🥗', color: '#10b981' },
      { text: 'Get enough sleep', icon: '🛌', color: '#6366f1' },
    ],
  },
  heart: {
    advices: [
      { text: 'Limit salt and processed foods', icon: '🧂', color: '#f87171' },
      { text: 'Eat more fruits and vegetables', icon: '🍎', color: '#34d399' },
      { text: 'Choose healthy fats', icon: '🥑', color: '#fbbf24' },
      { text: 'Reduce red meat intake', icon: '🥩', color: '#f472b6' },
    ],
    recommendedActions: [
      {
        text: 'Engage in regular aerobic exercise',
        icon: '🚴‍♂️',
        color: '#60a5fa',
      },
      { text: 'Monitor blood pressure', icon: '🩺', color: '#fbbf24' },
      { text: 'Quit smoking', icon: '🚭', color: '#f87171' },
    ],
  },
  prenatal: {
    advices: [
      {
        text: 'Eat a variety of nutrient-rich foods',
        icon: '🥦',
        color: '#34d399',
      },
      { text: 'Take prenatal vitamins', icon: '💊', color: '#fbbf24' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      { text: 'Avoid raw or undercooked foods', icon: '🍣', color: '#f87171' },
    ],
    recommendedActions: [
      { text: 'Consult your doctor regularly', icon: '👩‍⚕️', color: '#6366f1' },
      {
        text: 'Engage in light physical activity',
        icon: '🚶‍♀️',
        color: '#f59e42',
      },
    ],
  },
  sports: {
    advices: [
      { text: 'Increase protein intake', icon: '🍗', color: '#fbbf24' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      { text: 'Eat balanced meals', icon: '🥗', color: '#10b981' },
      { text: 'Refuel after workouts', icon: '🥤', color: '#f472b6' },
    ],
    recommendedActions: [
      { text: 'Warm up before exercise', icon: '🤸‍♂️', color: '#f59e42' },
      { text: 'Track your progress', icon: '📈', color: '#6366f1' },
    ],
  },
  recovery: {
    advices: [
      { text: 'Eat protein-rich foods', icon: '🥚', color: '#fbbf24' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      { text: 'Include fruits and vegetables', icon: '🍎', color: '#34d399' },
      { text: 'Avoid alcohol', icon: '🚫', color: '#f87171' },
    ],
    recommendedActions: [
      { text: 'Follow your doctor’s advice', icon: '👨‍⚕️', color: '#6366f1' },
      { text: 'Rest as needed', icon: '🛌', color: '#f59e42' },
    ],
  },
  senior: {
    advices: [
      { text: 'Focus on nutrient-dense foods', icon: '🥦', color: '#34d399' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      { text: 'Limit salt and sugar', icon: '🧂', color: '#f87171' },
      { text: 'Eat enough fiber', icon: '🌾', color: '#fbbf24' },
    ],
    recommendedActions: [
      { text: 'Stay physically active', icon: '🚶‍♂️', color: '#f59e42' },
      { text: 'Get regular checkups', icon: '🩺', color: '#6366f1' },
    ],
  },
  gluten: {
    advices: [
      { text: 'Read food labels carefully', icon: '🔖', color: '#fbbf24' },
      { text: 'Avoid wheat, barley, and rye', icon: '🚫', color: '#f87171' },
      {
        text: 'Choose certified gluten-free products',
        icon: '✅',
        color: '#34d399',
      },
    ],
    recommendedActions: [
      { text: 'Plan meals ahead', icon: '📅', color: '#f59e42' },
      { text: 'Consult a dietitian', icon: '👩‍⚕️', color: '#6366f1' },
    ],
  },
  diabetes: {
    advices: [
      { text: 'Monitor carbohydrate intake', icon: '🍞', color: '#fbbf24' },
      { text: 'Eat regular meals', icon: '⏰', color: '#60a5fa' },
      { text: 'Limit sugary foods', icon: '🍬', color: '#f472b6' },
      { text: 'Stay active', icon: '🏃‍♂️', color: '#10b981' },
    ],
    recommendedActions: [
      { text: 'Check blood sugar regularly', icon: '🩸', color: '#f87171' },
      { text: 'Follow medication schedule', icon: '💊', color: '#6366f1' },
    ],
  },
  gut: {
    advices: [
      { text: 'Eat fiber-rich foods', icon: '🌾', color: '#fbbf24' },
      { text: 'Include probiotics', icon: '🦠', color: '#34d399' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      { text: 'Limit processed foods', icon: '🚫', color: '#f87171' },
    ],
    recommendedActions: [
      { text: 'Chew food thoroughly', icon: '😋', color: '#f59e42' },
      { text: 'Manage stress', icon: '🧘‍♂️', color: '#6366f1' },
    ],
  },
  vegan: {
    advices: [
      { text: 'Eat a variety of plant foods', icon: '🥦', color: '#34d399' },
      { text: 'Ensure adequate protein', icon: '🌱', color: '#10b981' },
      { text: 'Supplement vitamin B12', icon: '💊', color: '#fbbf24' },
      { text: 'Plan meals for balance', icon: '📅', color: '#f59e42' },
    ],
    recommendedActions: [
      {
        text: 'Read labels for hidden animal products',
        icon: '🔖',
        color: '#6366f1',
      },
      { text: 'Consult a dietitian', icon: '👩‍⚕️', color: '#60a5fa' },
    ],
  },
  muscle: {
    advices: [
      { text: 'Increase protein intake', icon: '🍗', color: '#fbbf24' },
      { text: 'Eat frequent meals', icon: '⏰', color: '#60a5fa' },
      { text: 'Stay hydrated', icon: '💧', color: '#34d399' },
      { text: 'Get enough sleep', icon: '🛌', color: '#6366f1' },
    ],
    recommendedActions: [
      { text: 'Strength train regularly', icon: '🏋️‍♂️', color: '#f59e42' },
      { text: 'Track your progress', icon: '📈', color: '#10b981' },
    ],
  },
  skin: {
    advices: [
      { text: 'Eat antioxidant-rich foods', icon: '🍓', color: '#f472b6' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      { text: 'Limit processed foods', icon: '🚫', color: '#f87171' },
      { text: 'Include healthy fats', icon: '🥑', color: '#10b981' },
    ],
    recommendedActions: [
      { text: 'Use sunscreen daily', icon: '🧴', color: '#fbbf24' },
      { text: 'Get enough sleep', icon: '🛌', color: '#6366f1' },
    ],
  },
  detox: {
    advices: [
      { text: 'Drink plenty of water', icon: '💧', color: '#60a5fa' },
      { text: 'Eat fruits and vegetables', icon: '🍎', color: '#34d399' },
      { text: 'Limit caffeine and alcohol', icon: '🚫', color: '#f87171' },
    ],
    recommendedActions: [
      { text: 'Get enough rest', icon: '🛌', color: '#6366f1' },
      { text: 'Engage in light activity', icon: '🚶‍♂️', color: '#f59e42' },
    ],
  },
  mediterranean: {
    advices: [
      { text: 'Eat more fruits and vegetables', icon: '🍅', color: '#34d399' },
      { text: 'Use olive oil as main fat', icon: '🫒', color: '#fbbf24' },
      { text: 'Include fish regularly', icon: '🐟', color: '#60a5fa' },
      { text: 'Limit red meat', icon: '🥩', color: '#f87171' },
    ],
    recommendedActions: [
      { text: 'Enjoy meals with family', icon: '👨‍👩‍👧‍👦', color: '#10b981' },
      { text: 'Be physically active', icon: '🚶‍♂️', color: '#f59e42' },
    ],
  },
  keto: {
    advices: [
      {
        text: 'Limit carbs, focus on healthy fats',
        icon: '🥑',
        color: '#10b981',
      },
      { text: 'Monitor ketone levels', icon: '🧪', color: '#6366f1' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      { text: 'Watch for nutrient deficiencies', icon: '🔬', color: '#fbbf24' },
    ],
    recommendedActions: [
      { text: 'Plan meals ahead', icon: '📅', color: '#f59e42' },
      { text: 'Consult a healthcare provider', icon: '👩‍⚕️', color: '#6366f1' },
    ],
  },
  intermittent: {
    advices: [
      { text: 'Stick to your eating window', icon: '⏰', color: '#fbbf24' },
      { text: 'Stay hydrated during fasting', icon: '💧', color: '#60a5fa' },
      {
        text: 'Break your fast with balanced meals',
        icon: '🥗',
        color: '#10b981',
      },
    ],
    recommendedActions: [
      { text: 'Avoid overeating after fasting', icon: '🚫', color: '#f87171' },
      { text: 'Listen to your body', icon: '🧘‍♂️', color: '#6366f1' },
    ],
  },
  dash: {
    advices: [
      { text: 'Eat more fruits and vegetables', icon: '🍎', color: '#34d399' },
      { text: 'Limit salt and sodium', icon: '🧂', color: '#f87171' },
      { text: 'Choose low-fat dairy', icon: '🥛', color: '#60a5fa' },
    ],
    recommendedActions: [
      { text: 'Read nutrition labels', icon: '🔖', color: '#fbbf24' },
      { text: 'Be physically active', icon: '🚶‍♂️', color: '#f59e42' },
    ],
  },
  paleo: {
    advices: [
      {
        text: 'Focus on lean meats and vegetables',
        icon: '🥩',
        color: '#fbbf24',
      },
      { text: 'Avoid processed foods', icon: '🚫', color: '#f87171' },
      { text: 'Eat nuts and seeds', icon: '🥜', color: '#10b981' },
    ],
    recommendedActions: [
      { text: 'Plan meals ahead', icon: '📅', color: '#f59e42' },
      { text: 'Stay active', icon: '🏃‍♂️', color: '#60a5fa' },
    ],
  },
};
