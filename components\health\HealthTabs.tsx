import React from 'react';
import { View } from 'react-native';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from '@/components/ui/tabs';
import { Text } from '@/components/ui/text';

interface HealthTabsProps {
  children: React.ReactNode;
}

const HealthTabs: React.FC<HealthTabsProps> = ({ children }) => {
  const [activeTab, setActiveTab] = React.useState('physical');

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
      <TabsList className="flex flex-row justify-around p-2 bg-gray-100 dark:bg-gray-800 rounded-lg mb-4">
        <TabsTrigger value="physical" className="flex-1 p-2">
          <Text className="text-base">Physical Health</Text>
        </TabsTrigger>
        <TabsTrigger value="mental" className="flex-1 p-2">
          <Text className="text-base">Mental Health</Text>
        </TabsTrigger>
        <TabsTrigger value="nutrition" className="flex-1 p-2">
          <Text className="text-base">Nutrition</Text>
        </TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
};

export default HealthTabs;
