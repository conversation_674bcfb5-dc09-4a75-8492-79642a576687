import React, { useCallback, useRef, useMemo } from 'react';
import { View } from 'react-native';
import { Button } from '@/components/ui/button';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { Text } from '@/components/ui/text';
import { CreateHealthPlanForm } from '@/components/health/CreateHealthPlanForm';

const ChangeHealthPlanButton: React.FC = () => {
  const sheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);
  const handleSnapPress = useCallback((index: number) => {
    sheetRef.current?.snapToIndex(index);
  }, []);

  const handleClosePress = useCallback(() => {
    sheetRef.current?.close();
  }, []);

  return (
    <View>
      <Button onPress={() => handleSnapPress(1)} className="mb-4">
        <Text>Change Health Plan</Text>
      </Button>
      <BottomSheet
        ref={sheetRef}
        snapPoints={snapPoints}
        enableDynamicSizing={false}
        index={-1} // Start closed
        enablePanDownToClose={true}
      >
        <BottomSheetView className="flex-1 p-4">
          <Text className="text-2xl font-bold mb-4">
            Create/Update Health Plan
          </Text>
          <CreateHealthPlanForm
            onPlanCreated={handleClosePress}
            onCancel={handleClosePress}
          />
        </BottomSheetView>
      </BottomSheet>
    </View>
  );
};

export default ChangeHealthPlanButton;
