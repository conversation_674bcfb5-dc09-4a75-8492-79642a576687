import React from 'react';
import { View } from 'react-native';
import { Droplet, Apple, Fish, Wheat } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Card } from '../ui/card';
import { Text } from '../ui/text';
import { Progress } from '../ui/progress';
import { theme } from '@/constants/theme';

const AnimatedCard = Animated.createAnimatedComponent(Card);

interface DailyOverviewProps {
  mealsSummary:
    | {
        totalCalories: number;
        totalProtein: number;
        totalCarbs: number;
        totalFat: number;
        mealsByType: {
          breakfast: number;
          lunch: number;
          dinner: number;
          snacks: number;
        };
      }
    | undefined;
}

interface StatCardProps {
  icon: React.ReactElement;
  label: string;
  value: number;
  target: number;
  unit: string;
  bgColor: string;
  iconColor: string;
  textColor: string;
}

const StatCard: React.FC<StatCardProps> = ({
  icon,
  label,
  value,
  target,
  unit,
  bgColor,
  iconColor,
  textColor,
}) => (
  <View
    className="flex-1 items-center p-4 rounded-2xl m-1"
    style={{ backgroundColor: bgColor }}
  >
    <View className="flex-row items-center justify-center mb-2">
      {React.cloneElement(icon, {
        size: 18,
        color: iconColor,
      })}
      <Text
        className="font-semibold text-base ml-2"
        style={{ color: textColor }}
      >
        {label}
      </Text>
    </View>
    <Text className="font-bold text-2xl" style={{ color: textColor }}>
      {value}
      <Text
        className="font-normal text-sm"
        style={{ color: textColor, opacity: 0.8 }}
      >
        {' '}
        / {target} {unit}
      </Text>
    </Text>
  </View>
);

export function DailyOverview({ mealsSummary }: DailyOverviewProps) {
  // Define default values for when mealsSummary is undefined
  const defaultMealsSummary = {
    totalCalories: 0,
    totalProtein: 0,
    totalCarbs: 0,
    totalFat: 0,
    mealsByType: {
      breakfast: 0,
      lunch: 0,
      dinner: 0,
      snacks: 0,
    },
  };

  // Use provided mealsSummary or fall back to defaults
  const summary = mealsSummary || defaultMealsSummary;

  // Define target values (these could also come from a health plan or user settings)
  const targetCalories = 2000;
  const targetProtein = 150;
  const targetCarbs = 250;
  const targetFats = 65;
  const targetHydration = 2.5; // Liters, assuming this is tracked separately or hardcoded for now

  const currentHydration = 1.2; // Placeholder, as hydration is not in mealsSummary

  const calorieProgress = (summary.totalCalories / targetCalories) * 100;

  return (
    <AnimatedCard
      className="mx-6 mt-4 shadow-lg border-0 bg-white"
      entering={FadeInDown.delay(200)}
    >
      <View className="flex-row justify-between items-center mb-4">
        <Text className="font-bold text-xl text-gray-900">Daily Overview</Text>
        <Text className="font-semibold text-lg text-gray-700">
          {Math.round(summary.totalCalories)} / {targetCalories} kcal
        </Text>
      </View>

      <View className="h-4 w-full bg-gray-200 rounded-full mb-4">
        <Progress
          value={calorieProgress}
          className="h-4"
          indicatorClassName="bg-primary"
        />
      </View>

      <View className="flex-row">
        <StatCard
          icon={<Fish />}
          label="Protein"
          value={summary.totalProtein}
          target={targetProtein}
          unit="g"
          bgColor={theme.colors.primaryLight}
          iconColor={theme.colors.primary}
          textColor={theme.colors.secondary}
        />
        <StatCard
          icon={<Wheat />}
          label="Carbs"
          value={summary.totalCarbs}
          target={targetCarbs}
          unit="g"
          bgColor={'#FFF8E1'}
          iconColor={theme.colors.warning}
          textColor={theme.colors.secondary}
        />
      </View>
      <View className="flex-row mt-2">
        <StatCard
          icon={<Apple />}
          label="Fats"
          value={summary.totalFat}
          target={targetFats}
          unit="g"
          bgColor={'#FFEBEE'}
          iconColor={theme.colors.error}
          textColor={theme.colors.secondary}
        />
        <StatCard
          icon={<Droplet />}
          label="Water"
          value={currentHydration}
          target={targetHydration}
          unit="L"
          bgColor={'#F1F8E9'}
          iconColor={theme.colors.success}
          textColor={theme.colors.secondary}
        />
      </View>
    </AnimatedCard>
  );
}
