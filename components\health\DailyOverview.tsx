import React from 'react';
import { View } from 'react-native';
import { Droplet, Apple, Fish, Wheat } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Card } from '../ui/card';
import { Text } from '../ui/text';
import { Progress } from '../ui/progress';
import { theme } from '@/constants/theme';
import { Doc } from '@/convex/_generated/dataModel';
import { healthPlansData } from '@/data/healthPlansData';

const AnimatedCard = Animated.createAnimatedComponent(Card);

type HealthPlan = Doc<'healthPlan'>;

interface DailyOverviewProps {
  mealsSummary:
    | {
        totalCalories: number;
        totalProtein: number;
        totalCarbs: number;
        totalFat: number;
        mealsByType: {
          breakfast: number;
          lunch: number;
          dinner: number;
          snacks: number;
        };
      }
    | undefined;
  activePlan?: HealthPlan;
}

interface StatCardProps {
  icon: React.ReactElement;
  label: string;
  value: number;
  target: number;
  unit: string;
  bgColor: string;
  iconColor: string;
  textColor: string;
}

const StatCard: React.FC<StatCardProps> = ({
  icon,
  label,
  value,
  target,
  unit,
  bgColor,
  iconColor,
  textColor,
}) => (
  <View
    className="flex-1 items-center p-4 rounded-2xl m-1"
    style={{ backgroundColor: bgColor }}
  >
    <View className="flex-row items-center justify-center mb-2">
      {React.cloneElement(icon, {
        // @ts-ignore
        size: 18,
        color: iconColor,
      })}
      <Text
        className="font-semibold text-base ml-2"
        style={{ color: textColor }}
      >
        {label}
      </Text>
    </View>
    <Text className="font-bold text-2xl" style={{ color: textColor }}>
      {value}
      <Text
        className="font-normal text-sm"
        style={{ color: textColor, opacity: 0.8 }}
      >
        {' '}
        / {target} {unit}
      </Text>
    </Text>
  </View>
);

export function DailyOverview({
  mealsSummary,
  activePlan,
}: DailyOverviewProps) {
  // Define default values for when mealsSummary is undefined
  const defaultMealsSummary = {
    totalCalories: 0,
    totalProtein: 0,
    totalCarbs: 0,
    totalFat: 0,
    mealsByType: {
      breakfast: 0,
      lunch: 0,
      dinner: 0,
      snacks: 0,
    },
  };

  // Use provided mealsSummary or fall back to defaults
  const summary = mealsSummary || defaultMealsSummary;

  // Get nutrition targets from active plan or use defaults
  const getNutritionTargets = () => {
    if (activePlan?.dailyNutrition) {
      return {
        calories: activePlan.dailyNutrition.calories,
        protein: activePlan.dailyNutrition.protein,
        carbs: activePlan.dailyNutrition.carbs,
        fat: activePlan.dailyNutrition.fat,
        water: activePlan.dailyNutrition.water / 1000, // Convert ml to liters
      };
    }

    // If no plan nutrition data, try to get from healthPlansData
    if (activePlan?.type) {
      const planData = healthPlansData.find(
        (plan) => plan.type === activePlan.type,
      );
      if (planData) {
        return {
          calories: planData.dailyNutrition.calories,
          protein: planData.dailyNutrition.protein,
          carbs: planData.dailyNutrition.carbs,
          fat: planData.dailyNutrition.fat,
          water: planData.dailyNutrition.water / 1000, // Convert ml to liters
        };
      }
    }

    // Default fallback values
    return {
      calories: 2000,
      protein: 150,
      carbs: 250,
      fat: 65,
      water: 2.5,
    };
  };

  const targets = getNutritionTargets();
  const currentHydration = 1.2; // Placeholder, as hydration is not in mealsSummary

  const calorieProgress = (summary.totalCalories / targets.calories) * 100;

  return (
    <AnimatedCard
      className="mx-6 mt-4 shadow-lg border-0 bg-white"
      entering={FadeInDown.delay(200)}
    >
      <View className="flex-row justify-between items-center mb-4">
        <Text className="font-bold text-xl text-gray-900">Daily Overview</Text>
        <Text className="font-semibold text-lg text-gray-700">
          {Math.round(summary.totalCalories)} / {targets.calories} kcal
        </Text>
      </View>

      <View className="h-4 w-full bg-gray-200 rounded-full mb-4">
        <Progress
          value={calorieProgress}
          className="h-4"
          indicatorClassName="bg-primary"
        />
      </View>

      <View className="flex-row">
        <StatCard
          icon={<Fish />}
          label="Protein"
          value={summary.totalProtein}
          target={targets.protein}
          unit="g"
          bgColor={theme.colors.primaryLight}
          iconColor={theme.colors.primary}
          textColor={theme.colors.secondary}
        />
        <StatCard
          icon={<Wheat />}
          label="Carbs"
          value={summary.totalCarbs}
          target={targets.carbs}
          unit="g"
          bgColor={'#FFF8E1'}
          iconColor={theme.colors.warning}
          textColor={theme.colors.secondary}
        />
      </View>
      <View className="flex-row mt-2">
        <StatCard
          icon={<Apple />}
          label="Fats"
          value={summary.totalFat}
          target={targets.fat}
          unit="g"
          bgColor={'#FFEBEE'}
          iconColor={theme.colors.error}
          textColor={theme.colors.secondary}
        />
        <StatCard
          icon={<Droplet />}
          label="Water"
          value={currentHydration}
          target={targets.water}
          unit="L"
          bgColor={'#F1F8E9'}
          iconColor={theme.colors.success}
          textColor={theme.colors.secondary}
        />
      </View>
    </AnimatedCard>
  );
}
