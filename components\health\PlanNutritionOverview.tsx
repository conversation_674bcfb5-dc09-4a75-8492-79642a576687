import React from 'react';
import { View } from 'react-native';
import { Doc } from '@/convex/_generated/dataModel';
import { healthPlansData, HealthPlanType } from '@/data/healthPlansData';
import { Text } from '../ui/text';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';

type HealthPlan = Doc<'healthPlan'>;

interface PlanNutritionOverviewProps {
  activePlan: HealthPlan;
}

export function PlanNutritionOverview({ activePlan }: PlanNutritionOverviewProps) {
  const planData = healthPlansData.find((plan) => plan.type === activePlan.type);

  if (!planData) {
    return null;
  }

  const { dailyNutrition, keyBenefits, recommendedDuration } = planData;

  const nutritionItems = [
    { label: 'Calories', value: `${dailyNutrition.calories}`, unit: 'kcal' },
    { label: 'Protein', value: `${dailyNutrition.protein}`, unit: 'g' },
    { label: 'Carbs', value: `${dailyNutrition.carbs}`, unit: 'g' },
    { label: 'Fat', value: `${dailyNutrition.fat}`, unit: 'g' },
    { label: 'Fiber', value: `${dailyNutrition.fiber}`, unit: 'g' },
    { label: 'Water', value: `${dailyNutrition.water}`, unit: 'ml' },
  ];

  return (
    <View className="mx-4 mt-4">
      <Card className="rounded-lg shadow-md bg-card">
        <CardContent className="p-4">
          <Text className="font-bold text-lg text-foreground mb-4">
            Daily Nutrition Targets
          </Text>
          
          {/* Nutrition Grid */}
          <View className="flex-row flex-wrap justify-between mb-6">
            {nutritionItems.map((item, index) => (
              <View key={index} className="w-[48%] mb-4">
                <View className="bg-secondary/50 rounded-lg p-3">
                  <Text className="text-xs text-muted-foreground mb-1">
                    {item.label}
                  </Text>
                  <Text className="font-bold text-lg text-foreground">
                    {item.value}
                    <Text className="text-sm text-muted-foreground"> {item.unit}</Text>
                  </Text>
                </View>
              </View>
            ))}
          </View>

          {/* Key Benefits */}
          <View className="mb-4">
            <Text className="font-semibold text-base text-foreground mb-3">
              Key Benefits
            </Text>
            <View className="flex-row flex-wrap gap-2">
              {keyBenefits.map((benefit, index) => (
                <Badge key={index} variant="secondary" className="mb-2">
                  <Text className="text-xs">{benefit}</Text>
                </Badge>
              ))}
            </View>
          </View>

          {/* Recommended Duration */}
          <View className="pt-4 border-t border-border">
            <Text className="text-sm text-muted-foreground">
              <Text className="font-medium">Recommended Duration:</Text> {recommendedDuration}
            </Text>
          </View>
        </CardContent>
      </Card>
    </View>
  );
}
