/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as aiAdvices from "../aiAdvices.js";
import type * as drinks from "../drinks.js";
import type * as exercisePlans from "../exercisePlans.js";
import type * as fitness from "../fitness.js";
import type * as healthPlans from "../healthPlans.js";
import type * as meals from "../meals.js";
import type * as meditation from "../meditation.js";
import type * as mood from "../mood.js";
import type * as sleep from "../sleep.js";
import type * as uploads from "../uploads.js";
import type * as userProfile from "../userProfile.js";
import type * as users from "../users.js";
import type * as wellbeing from "../wellbeing.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  aiAdvices: typeof aiAdvices;
  drinks: typeof drinks;
  exercisePlans: typeof exercisePlans;
  fitness: typeof fitness;
  healthPlans: typeof healthPlans;
  meals: typeof meals;
  meditation: typeof meditation;
  mood: typeof mood;
  sleep: typeof sleep;
  uploads: typeof uploads;
  userProfile: typeof userProfile;
  users: typeof users;
  wellbeing: typeof wellbeing;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
