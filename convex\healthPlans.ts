import { query, mutation } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Health Plan Queries
export const getHealthPlans = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return await ctx.db
      .query('healthPlan')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .collect();
  },
});

export const getActiveHealthPlan = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const now = new Date().toISOString();
    const activePlan = await ctx.db
      .query('activeHealthPlan')
      .withIndex('by_user', (q) => q.eq('userId', identity.subject))
      .filter((q) =>
        q.and(
          q.eq(q.field('isDeleted'), false),
          q.lte(q.field('startDate'), now),
          q.or(
            q.eq(q.field('endDate'), undefined),
            q.gte(q.field('endDate'), now),
          ),
        ),
      )
      .first();

    if (!activePlan) return null;
    return await ctx.db.get(activePlan.planId);
  },
});

// Health Plan Mutations
export const createHealthPlan = mutation({
  args: {
    isActive: v.boolean(),
    dailyCalories: v.object({
      breakfast: v.number(),
      lunch: v.number(),
      dinner: v.number(),
      snacks: v.number(),
    }),
    type: v.union(
      v.literal('weight'),
      v.literal('heart'),
      v.literal('prenatal'),
      v.literal('sports'),
      v.literal('recovery'),
      v.literal('senior'),
      v.literal('gluten'),
      v.literal('diabetes'),
      v.literal('gut'),
      v.literal('vegan'),
      v.literal('muscle'),
      v.literal('skin'),
      v.literal('detox'),
      v.literal('mediterranean'),
      v.literal('keto'),
      v.literal('intermittent'),
      v.literal('dash'),
      v.literal('paleo'),
    ),
    startDate: v.string(),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const planId = await ctx.db.insert('healthPlan', {
      ...args,
      userId: identity.subject,
    });

    if (args.isActive) {
      // Deactivate other active plans
      await ctx.db
        .query('activeHealthPlan')
        .withIndex('by_user', (q) => q.eq('userId', identity.subject))
        .filter((q) => q.eq(q.field('isDeleted'), false))
        .collect()
        .then((activePlans) =>
          Promise.all(
            activePlans.map((plan) =>
              ctx.db.patch(plan._id, { isDeleted: true }),
            ),
          ),
        );

      // Mark new plan as active
      await ctx.db.insert('activeHealthPlan', {
        userId: identity.subject,
        planId,
        startDate: args.startDate,
        endDate: args.endDate,
        isDeleted: false,
      });
    }

    return planId;
  },
});

export const updateHealthPlan = mutation({
  args: {
    id: v.id('healthPlan'),
    updates: v.object({
      isActive: v.optional(v.boolean()),
      dailyCalories: v.optional(
        v.object({
          breakfast: v.number(),
          lunch: v.number(),
          dinner: v.number(),
          snacks: v.number(),
        }),
      ),
      // Add other updateable fields as needed
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const plan = await ctx.db.get(args.id);
    if (!plan) {
      throw new Error('Health plan not found');
    }

    if (plan.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    // Handle activation/deactivation
    if (args.updates.isActive !== undefined) {
      if (args.updates.isActive) {
        // Deactivate other active plans
        await ctx.db
          .query('activeHealthPlan')
          .withIndex('by_user', (q) => q.eq('userId', identity.subject))
          .filter((q) => q.eq(q.field('isDeleted'), false))
          .collect()
          .then((activePlans) =>
            Promise.all(
              activePlans.map((plan) =>
                ctx.db.patch(plan._id, { isDeleted: true }),
              ),
            ),
          );

        // Mark this plan as active
        await ctx.db.insert('activeHealthPlan', {
          userId: identity.subject,
          planId: args.id,
          startDate: new Date().toISOString(),
          isDeleted: false,
        });
      } else {
        // Deactivate this plan
        await ctx.db
          .query('activeHealthPlan')
          .withIndex('by_user', (q) => q.eq('userId', identity.subject))
          .filter((q) =>
            q.and(
              q.eq(q.field('planId'), args.id),
              q.eq(q.field('isDeleted'), false),
            ),
          )
          .collect()
          .then((activePlans) =>
            Promise.all(
              activePlans.map((plan) =>
                ctx.db.patch(plan._id, {
                  isDeleted: true,
                  endDate: new Date().toISOString(),
                }),
              ),
            ),
          );
      }
    }

    // Update the plan itself
    const { isActive, ...updates } = args.updates;
    if (Object.keys(updates).length > 0) {
      await ctx.db.patch(args.id, updates);
    }

    return { success: true };
  },
});

export const deleteHealthPlan = mutation({
  args: { id: v.id('healthPlan') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const plan = await ctx.db.get(args.id);
    if (!plan) {
      throw new Error('Health plan not found');
    }

    if (plan.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    // Mark any active plan records as deleted
    await ctx.db
      .query('activeHealthPlan')
      .withIndex('by_user', (q) => q.eq('userId', identity.subject))
      .filter((q) =>
        q.and(
          q.eq(q.field('planId'), args.id),
          q.eq(q.field('isDeleted'), false),
        ),
      )
      .collect()
      .then((activePlans) =>
        Promise.all(
          activePlans.map((plan) =>
            ctx.db.patch(plan._id, {
              isDeleted: true,
              endDate: new Date().toISOString(),
            }),
          ),
        ),
      );

    // Delete the plan
    await ctx.db.delete(args.id);
    return { success: true };
  },
});
