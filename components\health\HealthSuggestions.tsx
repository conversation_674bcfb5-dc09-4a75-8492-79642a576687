import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { theme } from '@/constants/theme';
import { Lightbulb, ArrowRight, X } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '../ui/dialog';
import { Text } from '../ui/text';
import { Doc } from '@/convex/_generated/dataModel';
import { planAdvices, PlanAdvice } from '@/data/planAdvices';

const AnimatedView = Animated.createAnimatedComponent(View);

type HealthPlan = Doc<'healthPlan'>;

interface HealthSuggestionsProps {
  activePlan?: HealthPlan;
}

type Suggestion = {
  text: string;
  icon: string;
  color: string;
  type: 'advice' | 'action';
};

// Default suggestions when no plan is active
const defaultSuggestions: Suggestion[] = [
  {
    text: 'Create a health plan to get personalized advice',
    icon: '📋',
    color: '#6366f1',
    type: 'advice',
  },
  {
    text: 'Track your meals daily for better insights',
    icon: '📝',
    color: '#34d399',
    type: 'advice',
  },
  {
    text: 'Stay hydrated throughout the day',
    icon: '💧',
    color: '#60a5fa',
    type: 'advice',
  },
];

export function HealthSuggestions({ activePlan }: HealthSuggestionsProps) {
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);

  // Get plan-specific suggestions or use defaults
  const getSuggestions = (): Suggestion[] => {
    if (!activePlan) {
      return defaultSuggestions;
    }

    const planAdviceData = planAdvices[activePlan.type];
    if (!planAdviceData) {
      return defaultSuggestions;
    }

    // Combine advices and recommended actions
    const allSuggestions: Suggestion[] = [
      ...planAdviceData.advices.map((advice) => ({
        ...advice,
        type: 'advice' as const,
      })),
      ...planAdviceData.recommendedActions.map((action) => ({
        ...action,
        type: 'action' as const,
      })),
    ];

    return allSuggestions;
  };

  const suggestions = getSuggestions();

  return (
    <AnimatedView style={styles.container} entering={FadeInDown.delay(400)}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {activePlan ? 'Plan Suggestions' : 'Health Tips'}
        </Text>
        <TouchableOpacity
          style={styles.viewAllButton}
          onPress={() => setBottomSheetVisible(true)}
        >
          <Text style={styles.viewAllText}>View All</Text>
          <ArrowRight size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.suggestionsContainer}
      >
        {suggestions.slice(0, 3).map((suggestion, index) => (
          <TouchableOpacity
            key={index}
            style={styles.suggestionCard}
            onPress={() => setBottomSheetVisible(true)}
          >
            <View
              style={[
                styles.suggestionIcon,
                {
                  backgroundColor: `${suggestion.color}20`,
                },
              ]}
            >
              <Text style={{ fontSize: 24 }}>{suggestion.icon}</Text>
            </View>
            <Text style={styles.suggestionTitle}>{suggestion.text}</Text>
            <Text style={styles.suggestionDescription}>
              {suggestion.type === 'advice'
                ? 'Health Advice'
                : 'Recommended Action'}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <Dialog open={isBottomSheetVisible} onOpenChange={setBottomSheetVisible}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {activePlan ? 'Plan-Specific Suggestions' : 'Health Tips'}
            </DialogTitle>
            <DialogDescription>
              {activePlan
                ? `Personalized advice for your ${activePlan.type} health plan.`
                : 'General health tips to get you started.'}
            </DialogDescription>
          </DialogHeader>

          <ScrollView style={styles.bottomSheetContent}>
            {suggestions.map((suggestion, index) => (
              <View key={index} style={styles.fullSuggestionCard}>
                <View style={styles.fullSuggestionHeader}>
                  <View
                    style={[
                      styles.priorityIndicator,
                      {
                        backgroundColor: suggestion.color,
                      },
                    ]}
                  />
                  <Text style={{ fontSize: 20, marginRight: 8 }}>
                    {suggestion.icon}
                  </Text>
                  <Text style={styles.fullSuggestionTitle}>
                    {suggestion.text}
                  </Text>
                </View>
                <View style={styles.tagContainer}>
                  <Text
                    style={[
                      styles.tag,
                      {
                        backgroundColor: `${suggestion.color}20`,
                        color: suggestion.color,
                      },
                    ]}
                  >
                    {suggestion.type === 'advice'
                      ? 'Health Advice'
                      : 'Recommended Action'}
                  </Text>
                </View>
              </View>
            ))}
          </ScrollView>
        </DialogContent>
      </Dialog>
    </AnimatedView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.l,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    marginHorizontal: theme.spacing.l,
    marginTop: theme.spacing.l,
    ...theme.shadows.medium,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  title: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.primary,
    marginRight: theme.spacing.xs,
  },
  suggestionsContainer: {
    paddingVertical: theme.spacing.s,
  },
  suggestionCard: {
    width: 250,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginRight: theme.spacing.l,
  },
  suggestionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  suggestionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.s,
  },
  suggestionDescription: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    lineHeight: 20,
  },
  bottomSheetContent: {
    padding: theme.spacing.l,
  },
  fullSuggestionCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  fullSuggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
  priorityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: theme.spacing.s,
  },
  fullSuggestionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    flex: 1,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.s,
  },
  tag: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.xs,
    paddingHorizontal: theme.spacing.s,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.s,
    overflow: 'hidden',
    textTransform: 'capitalize',
  },
});
