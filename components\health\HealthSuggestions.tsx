import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { theme } from '@/constants/theme';
import { Lightbulb, ArrowRight, X } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '../ui/dialog';
import { Text } from '../ui/text';

const AnimatedView = Animated.createAnimatedComponent(View);

type Suggestion = {
  title: string;
  description: string;
  type: 'nutrition' | 'hydration' | 'planning' | 'exercise' | 'sleep';
  priority: 'high' | 'medium' | 'low';
};

const suggestions: Suggestion[] = [
  {
    title: 'Increase Protein Intake',
    description:
      'Try adding lean meats or legumes to your next meal to meet your protein goals. This will help with muscle recovery and satiety.',
    type: 'nutrition',
    priority: 'high',
  },
  {
    title: 'Hydration Reminder',
    description:
      "You're below your water intake goal. Try drinking a glass of water now. Staying hydrated helps with energy levels and cognitive function.",
    type: 'hydration',
    priority: 'medium',
  },
  {
    title: 'Meal Planning',
    description:
      'Based on your health plan, consider preparing meals with more whole grains. They provide sustained energy and important nutrients.',
    type: 'planning',
    priority: 'low',
  },
  {
    title: 'Post-Workout Nutrition',
    description:
      'Schedule your protein-rich meals within 30 minutes after exercise for optimal recovery.',
    type: 'nutrition',
    priority: 'high',
  },
  {
    title: 'Sleep Quality',
    description:
      'Consider avoiding caffeine after 2 PM to improve sleep quality and recovery.',
    type: 'sleep',
    priority: 'medium',
  },
  {
    title: 'Exercise Timing',
    description:
      'Based on your meal patterns, the best time for your workout would be between 9-10 AM.',
    type: 'exercise',
    priority: 'low',
  },
];

export function HealthSuggestions() {
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);

  const getPriorityColor = (priority: Suggestion['priority']) => {
    switch (priority) {
      case 'high':
        return theme.colors.error;
      case 'medium':
        return theme.colors.warning;
      case 'low':
        return theme.colors.accent;
      default:
        return theme.colors.gray[600];
    }
  };

  return (
    <AnimatedView style={styles.container} entering={FadeInDown.delay(400)}>
      <View style={styles.header}>
        <Text style={styles.title}>AI Suggestions</Text>
        <TouchableOpacity
          style={styles.viewAllButton}
          onPress={() => setBottomSheetVisible(true)}
        >
          <Text style={styles.viewAllText}>View All</Text>
          <ArrowRight size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.suggestionsContainer}
      >
        {suggestions.slice(0, 3).map((suggestion, index) => (
          <TouchableOpacity
            key={index}
            style={styles.suggestionCard}
            onPress={() => setBottomSheetVisible(true)}
          >
            <View
              style={[
                styles.suggestionIcon,
                {
                  backgroundColor: `${getPriorityColor(suggestion.priority)}20`,
                },
              ]}
            >
              <Lightbulb
                size={24}
                color={getPriorityColor(suggestion.priority)}
              />
            </View>
            <Text style={styles.suggestionTitle}>{suggestion.title}</Text>
            <Text style={styles.suggestionDescription}>
              {suggestion.description}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <Dialog open={isBottomSheetVisible} onOpenChange={setBottomSheetVisible}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>All Suggestions</DialogTitle>
            <DialogDescription>
              Here are all the health suggestions.
            </DialogDescription>
          </DialogHeader>

          <ScrollView style={styles.bottomSheetContent}>
            {suggestions.map((suggestion, index) => (
              <View key={index} style={styles.fullSuggestionCard}>
                <View style={styles.fullSuggestionHeader}>
                  <View style={[
                    styles.priorityIndicator,
                    { backgroundColor: getPriorityColor(suggestion.priority) }
                  ]} />
                  <Text style={styles.fullSuggestionTitle}>{suggestion.title}</Text>
                </View>
                <Text style={styles.fullSuggestionDescription}>
                  {suggestion.description}
                </Text>
                <View style={styles.tagContainer}>
                  <Text style={[
                    styles.tag,
                    { backgroundColor: `${getPriorityColor(suggestion.priority)}20`,
                      color: getPriorityColor(suggestion.priority) }
                  ]}>
                    {suggestion.type}
                  </Text>
                  <Text style={[
                    styles.tag,
                    { backgroundColor: `${getPriorityColor(suggestion.priority)}20`,
                      color: getPriorityColor(suggestion.priority) }
                  ]}>
                    {suggestion.priority} priority
                  </Text>
                </View>
              </View>
            ))}
          </ScrollView>
        </DialogContent>
      </Dialog>
    </AnimatedView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.l,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    marginHorizontal: theme.spacing.l,
    marginTop: theme.spacing.l,
    ...theme.shadows.medium,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  title: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.primary,
    marginRight: theme.spacing.xs,
  },
  suggestionsContainer: {
    paddingVertical: theme.spacing.s,
  },
  suggestionCard: {
    width: 250,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginRight: theme.spacing.l,
  },
  suggestionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  suggestionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.s,
  },
  suggestionDescription: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    lineHeight: 20,
  },
  bottomSheetContent: {
    padding: theme.spacing.l,
  },
  fullSuggestionCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  fullSuggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
  priorityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: theme.spacing.s,
  },
  fullSuggestionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    flex: 1,
  },
  fullSuggestionDescription: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[700],
    lineHeight: 24,
    marginBottom: theme.spacing.m,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.s,
  },
  tag: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.xs,
    paddingHorizontal: theme.spacing.s,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.s,
    overflow: 'hidden',
    textTransform: 'capitalize',
  },
});
